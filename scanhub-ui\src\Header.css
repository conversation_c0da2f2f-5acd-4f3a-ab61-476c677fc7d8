.header {
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  /* background-color: #fff; */
}

.header .logo {
  display: flex;
  align-items: center;
}

.header .logo span {
  font-weight: bold;
}

.header .logo strong {
  padding: 0 6px;
  border-radius: 4px;
  font-weight: 600;
}

.header .logo img {
  width: 25px;
  height: 25px;
  margin-right: 10px;
}

.header-flex {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Safari/iOS specific fix for toggler alignment */
@media not all and (min-resolution:.001dpcm) { @supports (-webkit-touch-callout: none) {
  .header-flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
  }
  .navbar-toggler {
    margin-left: auto !important;
    right: 0;
  }
}}

/* iOS Safari specific header fixes */
@supports (-webkit-touch-callout: none) {
  .navbar.fixed-top {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1030;
  }
}